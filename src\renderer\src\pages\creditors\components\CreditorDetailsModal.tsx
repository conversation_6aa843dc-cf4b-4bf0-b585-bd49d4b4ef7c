import { useEffect, useState } from 'react'
import { Modal, Form, Input, message, Spin, Row, Col, Statistic, Card, Button } from 'antd'
import { partyApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import { usePartyContext } from '@/renderer/contexts'

interface CreditorDetailsModalProps {
  creditorId: string | null
  open: boolean
  onClose: () => void
}

interface CreditorDetails {
  id: string
  name: string
  contact?: string
  address?: string
  phoneNumber?: string
  currentBalance: number
  _count: {
    CreditInvoice: number
    Payments: number
  }
}

export const CreditorDetailsModal = ({ creditorId, open, onClose }: CreditorDetailsModalProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [editing, setEditing] = useState(false)
  const [creditorDetails, setCreditorDetails] = useState<CreditorDetails | null>(null)

  const { refreshCreditors } = usePartyContext()

  useEffect(() => {
    if (creditorId && open) {
      fetchCreditorDetails()
    }
  }, [creditorId, open])

  const fetchCreditorDetails = async () => {
    if (!creditorId) return
    setLoading(true)
    try {
      const response = await partyApi.getParty(creditorId)
      const creditorData = response.data.data
      setCreditorDetails(creditorData)
      form.setFieldsValue({
        name: creditorData.name,
        contact: creditorData.contact,
        phoneNumber: creditorData.phoneNumber,
        address: creditorData.address
      })
    } catch (error) {
      message.error('Failed to fetch creditor details')
      onClose()
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async (values: any) => {
    if (!creditorId) return
    try {
      await partyApi.updateParty(creditorId, values)
      message.success('Creditor updated successfully')
      setEditing(false)
      fetchCreditorDetails()
      refreshCreditors()
    } catch (error) {
      message.error('Failed to update creditor')
    }
  }

  return (
    <Modal
      title="Creditor Details"
      open={open}
      onCancel={onClose}
      width={800}
      footer={
        editing
          ? [
              <Button key="cancel" onClick={() => setEditing(false)}>
                Cancel
              </Button>,
              <Button key="submit" type="primary" onClick={() => form.submit()}>
                Save Changes
              </Button>
            ]
          : [
              <Button key="edit" type="primary" onClick={() => setEditing(true)}>
                Edit
              </Button>
            ]
      }
    >
      {loading ? (
        <div className="loading-container">
          <Spin />
        </div>
      ) : (
        <>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Current Balance"
                  value={creditorDetails?.currentBalance || 0}
                  precision={2}
                  prefix="Rs."
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Total Credits"
                  value={creditorDetails?._count.CreditInvoice || 0}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Total Payments" value={creditorDetails?._count.Payments || 0} />
              </Card>
            </Col>
          </Row>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdate}
            disabled={!editing}
            style={{ marginTop: 24 }}
          >
            <Form.Item
              name="name"
              label="Creditor Name"
              rules={[{ required: true, message: 'Please enter creditor name' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="contact" label="Contact Person">
              <Input />
            </Form.Item>

            <Form.Item name="phoneNumber" label="Phone Number">
              <Input />
            </Form.Item>

            <Form.Item name="address" label="Address">
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  )
}
