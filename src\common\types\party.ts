import { Prisma } from "@prisma/client";

// export { PartyType, CustomerType }
export enum PartyType {
    VENDOR = 'VENDOR',
    CUSTOMER = 'CUSTOMER',
    CREDITOR = 'CREDITOR'
}

export enum CustomerType {
    WALK_IN = 'WALK_IN',
    REGISTERED = 'REGISTERED'
}

export interface GetPartiesParams {
    page: number;
    limit: number;
    search?: string;
    type?: PartyType;
    where?: Prisma.PartyWhereInput;
}

export interface GetPartiesByBalanceParams {
    page: number;
    limit: number;
    balanceType: 'positive' | 'negative' | 'zero';
}

export interface CreatePartyData {
    name: string;
    type: PartyType;
    contact?: string;
    address?: string;
    phoneNumber?: string;
    openingBalance?: number;
    createdById: string;
}

export interface CreatePartyParams {
    name: string;
    type: 'VENDOR' | 'CUSTOMER' | 'CREDITOR';
    contact?: string;
    address?: string;
    phoneNumber?: string;
    openingBalance?: number;
}

export interface UpdatePartyParams {
    contact?: string;
    address?: string;
    phoneNumber?: string;
}

export interface GetPartiesByTypeParams {
    type: PartyType;
    search?: string;
    sortBy?: 'name' | 'phoneNumber' | 'balance';
    sortOrder?: 'asc' | 'desc';
    excludeSettled?: boolean;
}

export interface PartyListItem {
    id: string;
    name: string;
    phoneNumber?: string;
    balance: number;
}

export interface PartySummary {
    totalCount: number;
    totalPositiveBalance: number;  // Payables (I owe them) - positive balances
    totalNegativeBalance: number;  // Receivables (They owe me) - negative balances
    netBalance: number;            // Receivables - Payables (positive means net receivable, negative means net payable)
}

export interface GetPartiesByTypeResponse {
    parties: PartyListItem[];
    summary: PartySummary;
}

