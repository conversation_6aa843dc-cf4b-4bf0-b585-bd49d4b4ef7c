import { useState } from 'react'
import { Button, Modal, Space, App, Checkbox } from 'antd'
import { FaFilePdf, FaPrint, FaDownload } from 'react-icons/fa'
import { partyApi } from '@/renderer/services'
import { generatePartyListPDF } from '@/renderer/utils/generatePartyListPDF'
import { PartyType } from '@/common/types/party'

interface PartyListPdfButtonProps {
  partyType: PartyType
  search?: string
  sortBy?: 'name' | 'phoneNumber' | 'balance'
  sortOrder?: 'asc' | 'desc'
}

const PartyListPdfButton = ({
  partyType,
  search,
  sortBy = 'name',
  sortOrder = 'asc'
}: PartyListPdfButtonProps) => {
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)
  const [excludeSettled, setExcludeSettled] = useState(false)

  const { message } = App.useApp()

  const handleGenerateReport = async () => {
    try {
      setLoading(true)
      const response = await partyApi.getPartiesByTypeForPDF({
        type: partyType,
        search,
        sortBy,
        sortOrder,
        excludeSettled
      })
      setLoading(false)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }

      setModalVisible(true)
    } catch (error: any) {
      setLoading(false)
      message.error(error.message || 'Failed to generate report')
    }
  }

  const handlePrint = async () => {
    try {
      setActionLoading(true)
      const response = await partyApi.getPartiesByTypeForPDF({
        type: partyType,
        search,
        sortBy,
        sortOrder,
        excludeSettled
      })

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      const doc = await generatePartyListPDF({
        parties: response.data.data.parties,
        summary: response.data.data.summary,
        partyType
      })

      if (doc) {
        const dataUri = doc.output('datauristring')
        await window.electron.ipcRenderer.invoke('print-pdf', dataUri)
      }

      setModalVisible(false)
      setActionLoading(false)
    } catch (error: any) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setActionLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setActionLoading(true)
      const response = await partyApi.getPartiesByTypeForPDF({
        type: partyType,
        search,
        sortBy,
        sortOrder,
        excludeSettled
      })

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      await generatePartyListPDF({
        parties: response.data.data.parties,
        summary: response.data.data.summary,
        partyType,
        shouldSave: true
      })

      message.success('PDF generated successfully')
      setModalVisible(false)
      setActionLoading(false)
    } catch (error: any) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setActionLoading(false)
    }
  }

  const handleCancel = () => {
    setModalVisible(false)
  }

  return (
    <>
      <Space direction="vertical" size="small">
        <Button
          type="primary"
          icon={<FaFilePdf />}
          onClick={handleGenerateReport}
          loading={loading}
        >
          Generate PDF
        </Button>
      </Space>

      <Modal
        title={`${partyType.charAt(0) + partyType.slice(1).toLowerCase()} List Report`}
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <p>What would you like to do with the report?</p>
        <Space className="mt-4">
          <Checkbox checked={excludeSettled} onChange={(e) => setExcludeSettled(e.target.checked)}>
            Exclude settled accounts (zero balance)
          </Checkbox>
          <Button type="primary" icon={<FaPrint />} onClick={handlePrint} loading={actionLoading}>
            Print
          </Button>
          <Button icon={<FaDownload />} onClick={handleSave} loading={actionLoading}>
            Save as PDF
          </Button>
          <Button onClick={handleCancel}>Cancel</Button>
        </Space>
      </Modal>
    </>
  )
}

export default PartyListPdfButton
