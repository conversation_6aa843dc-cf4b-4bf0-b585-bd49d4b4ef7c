import {
  Drawer,
  Form,
  DatePicker,
  Input,
  InputNumber,
  Space,
  Button,
  App,
  Typography,
  Select
} from 'antd'
import { useState } from 'react'
import { createAccountTransfer } from '@/renderer/services/accountTransfer'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'
import { usePartyContext } from '@/renderer/contexts'
import { CreateAccountTransferData } from '@/common/types/accountTransfer'

const { Title } = Typography
const { TextArea } = Input

interface AddAccountTransferDrawerProps {
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

export const AddAccountTransferDrawer = ({
  open,
  onClose,
  setRefreshTrigger
}: AddAccountTransferDrawerProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)
  const { vendors, customers, creditors } = usePartyContext()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: any) => {
    if (!user?.id) {
      message.error('User not found')
      return
    }

    try {
      setLoading(true)

      const transferData: CreateAccountTransferData = {
        description: values.description,
        amount: values.amount,
        transferDate: handleDatePickerValue(values.transferDate.toDate()),
        fromPartyId: values.fromPartyId,
        toPartyId: values.toPartyId,
        createdById: user.id
      }

      const response = await createAccountTransfer(transferData)

      if (response.error?.error || response.data?.error) {
        throw new Error(
          response.error?.message ||
            response.data?.error?.message ||
            'Failed to create account transfer'
        )
      }

      message.success('Account transfer created successfully')
      form.resetFields()
      setRefreshTrigger((prev: number) => prev + 1)
      onClose()
    } catch (error: any) {
      message.error(error.message || 'Failed to create account transfer')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  // Watch form values for validation
  const fromPartyId = Form.useWatch(['fromPartyId'], form)
  const toPartyId = Form.useWatch(['toPartyId'], form)

  // Combine all parties from context
  const allParties = [...vendors, ...customers, ...creditors]

  // Filter out the selected from party from the to party options and vice versa
  const fromPartyOptions = allParties.map((party) => ({
    label: party.label,
    value: party.value,
    disabled: party.value === toPartyId
  }))

  const toPartyOptions = allParties.map((party) => ({
    label: party.label,
    value: party.value,
    disabled: party.value === fromPartyId
  }))

  return (
    <Drawer
      title="Add Account Transfer"
      placement="right"
      width={720}
      onClose={handleClose}
      open={open}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          transferDate: null,
          amount: 0
        }}
      >
        <Form.Item
          name="transferDate"
          label="Transfer Date"
          rules={[{ required: true, message: 'Please select transfer date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Form.Item
            name="fromPartyId"
            label="From Party (Credited - Money Goes Out)"
            rules={[{ required: true, message: 'Please select from party' }]}
          >
            <Select
              placeholder="Select party to transfer from"
              options={fromPartyOptions}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            name="toPartyId"
            label="To Party (Debited - Money Comes In)"
            rules={[{ required: true, message: 'Please select to party' }]}
          >
            <Select
              placeholder="Select party to transfer to"
              options={toPartyOptions}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </div>

        <Form.Item
          name="amount"
          label="Transfer Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
          ]}
        >
          <InputNumber
            className="w-full"
            formatter={(value) => `PKR ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/PKR\s?|(,*)/g, '') as any}
            min={0.01}
            step={0.01}
          />
        </Form.Item>

        <Form.Item name="description" label="Description (Optional)">
          <TextArea rows={4} placeholder="Enter description for the transfer (optional)" />
        </Form.Item>

        {/* Transfer summary */}
        {fromPartyId && toPartyId && (
          <div className="mb-4 rounded border border-blue-200 bg-blue-50 p-3">
            <Title level={5} className="!mb-2 text-blue-800">
              Transfer Summary
            </Title>
            <div className="text-sm text-blue-700">
              <div className="mb-1">
                <strong>From:</strong> {allParties.find((p) => p.value === fromPartyId)?.label}
              </div>
              <div className="mb-2">
                <strong>To:</strong> {allParties.find((p) => p.value === toPartyId)?.label}
              </div>
              <div className="mt-2 rounded bg-blue-100 p-2 text-xs text-blue-600">
                <div className="mb-1 font-semibold">Accounting Impact:</div>
                <div>
                  • <strong>From Party:</strong> Gets CREDIT entry (money goes out, balance
                  increases)
                </div>
                <div>
                  • <strong>To Party:</strong> Gets DEBIT entry (money comes in, balance decreases)
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="absolute bottom-0 left-0 right-0 border-t bg-white p-4">
          <Space className="w-full justify-end">
            <Button onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" className="bg-blue-500" loading={loading}>
              Create Transfer
            </Button>
          </Space>
        </div>
      </Form>
    </Drawer>
  )
}
