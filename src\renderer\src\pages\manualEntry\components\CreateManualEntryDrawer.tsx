import { useState } from 'react'
import {
  Drawer,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Button,
  App,
  Radio,
  Divider
} from 'antd'
import { usePartyContext, useBankContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { manualEntryApi } from '@/renderer/services'
import { CreditDebit } from '@/common/types'
import type { CreateManualEntryData } from '@/common/types/manualEntry'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface CreateManualEntryDrawerProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export const CreateManualEntryDrawer = ({
  open,
  onClose,
  onSuccess
}: CreateManualEntryDrawerProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const currentUser = useSelector((state: IRootState) => state.user.data)

  const { vendors, customers, creditors } = usePartyContext()
  const { banks } = useBankContext()

  // Combine all parties for the dropdown
  const allParties = [
    ...vendors.map((v) => ({ ...v, type: 'VENDOR' })),
    ...customers.map((c) => ({ ...c, type: 'CUSTOMER' })),
    ...creditors.map((cr) => ({ ...cr, type: 'CREDITOR' }))
  ].sort((a, b) => a.label.localeCompare(b.label))

  const handleSubmit = async (values: any) => {
    if (!currentUser?.id) {
      message.error('User not authenticated')
      return
    }

    try {
      setLoading(true)

      // Validate that exactly one target is selected
      const targetCount = [values.partyId, values.bankId, values.targetType].filter(
        (x) => x !== undefined && x !== null
      ).length
      if (targetCount !== 1) {
        message.error('Please select exactly one target (party, bank, or target type)')
        return
      }

      const data: CreateManualEntryData = {
        amount: values.amount,
        description: values.description,
        transactionDate: values.transactionDate.toDate(),
        entryType: values.entryType,
        partyId: values.partyId,
        bankId: values.bankId,
        targetType: values.targetType,
        createdById: currentUser.id
      }

      await manualEntryApi.createManualEntry(data)
      message.success('Manual entry created successfully')
      form.resetFields()
      onSuccess()
    } catch (error: any) {
      message.error(error.message || 'Failed to create manual entry')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  const handleTargetTypeChange = () => {
    // Clear other target fields when target type changes
    form.setFieldsValue({
      partyId: undefined,
      bankId: undefined
    })
  }

  const handlePartyChange = () => {
    // Clear other target fields when party changes
    form.setFieldsValue({
      bankId: undefined,
      targetType: undefined
    })
  }

  const handleBankChange = () => {
    // Clear other target fields when bank changes
    form.setFieldsValue({
      partyId: undefined,
      targetType: undefined
    })
  }

  return (
    <Drawer
      title="Create Manual Entry"
      open={open}
      onClose={handleClose}
      width={600}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            Create Entry
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          transactionDate: dayjs(),
          entryType: CreditDebit.DEBIT
        }}
      >
        <Form.Item
          name="amount"
          label="Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
          ]}
        >
          <InputNumber
            className="w-full"
            placeholder="Enter amount"
            min={0.01}
            step={0.01}
            precision={2}
          />
        </Form.Item>

        <Form.Item
          name="entryType"
          label="Entry Type"
          rules={[{ required: true, message: 'Please select entry type' }]}
        >
          <Radio.Group>
            <Radio.Button value={CreditDebit.DEBIT}>Debit (-)</Radio.Button>
            <Radio.Button value={CreditDebit.CREDIT}>Credit (+)</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="transactionDate"
          label="Transaction Date"
          rules={[{ required: true, message: 'Please select transaction date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <TextArea rows={3} placeholder="Enter description (optional)" maxLength={500} showCount />
        </Form.Item>

        <Divider>Target Selection</Divider>

        <Form.Item name="targetType" label="Cash Location">
          <Select
            placeholder="Select cash location"
            showSearch
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            allowClear
            onChange={handleTargetTypeChange}
          >
            <Option value="CASH_VAULT">Cash Vault</Option>
            <Option value="SMALL_COUNTER">Small Counter</Option>
          </Select>
        </Form.Item>

        <Form.Item name="partyId" label="Party">
          <Select
            placeholder="Select party"
            allowClear
            showSearch
            onChange={handlePartyChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={allParties}
          />
        </Form.Item>

        <Form.Item name="bankId" label="Bank">
          <Select
            placeholder="Select bank"
            allowClear
            showSearch
            onChange={handleBankChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={banks}
          />
        </Form.Item>

        <div className="mt-4 rounded bg-gray-50 p-3 text-sm text-gray-500">
          <strong>Note:</strong> Please select exactly one target (Cash Location, Party, or Bank).
          The entry will adjust the balance of the selected target.
        </div>
      </Form>
    </Drawer>
  )
}
